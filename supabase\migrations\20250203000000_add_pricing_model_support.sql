-- Add support for comprehensive pricing models
-- This migration adds a new pricing_model column to support various pricing strategies
-- while maintaining backward compatibility with existing subscription_plan data

-- Add pricing_model column to projects table
ALTER TABLE public.projects 
ADD COLUMN pricing_model JSONB;

-- Create index for better performance when querying pricing models
CREATE INDEX idx_projects_pricing_model_type ON public.projects 
USING GIN ((pricing_model->>'type'));

-- Create index for subscription pricing queries
CREATE INDEX idx_projects_pricing_subscription ON public.projects 
USING GIN ((pricing_model->'subscription'->'plans'));

-- Create index for commission pricing queries  
CREATE INDEX idx_projects_pricing_commission ON public.projects 
USING GIN ((pricing_model->'commission'));

-- Function to migrate existing subscription_plan data to new pricing_model format
CREATE OR REPLACE FUNCTION migrate_subscription_plans_to_pricing_model()
RETURNS void AS $$
BEGIN
  -- Update projects that have content with subscription_plan but no pricing_model
  UPDATE public.projects 
  SET pricing_model = jsonb_build_object(
    'type', 'subscription',
    'subscription', jsonb_build_object(
      'plans', jsonb_build_array(
        jsonb_build_object(
          'name', COALESCE(content->'subscription_plan'->>'plan_name', 'Pro Plan'),
          'price', COALESCE(content->'subscription_plan'->>'price', '$15/month'),
          'billing_period', 'monthly',
          'features', COALESCE(content->'subscription_plan'->'features', '[]'::jsonb),
          'popular', true
        )
      ),
      'description', 'Monthly subscription plan'
    )
  )
  WHERE content IS NOT NULL 
    AND content->'subscription_plan' IS NOT NULL
    AND pricing_model IS NULL;
    
  RAISE NOTICE 'Migration completed: % projects updated', 
    (SELECT COUNT(*) FROM public.projects WHERE pricing_model IS NOT NULL);
END;
$$ LANGUAGE plpgsql;

-- Run the migration function
SELECT migrate_subscription_plans_to_pricing_model();

-- Drop the migration function as it's no longer needed
DROP FUNCTION migrate_subscription_plans_to_pricing_model();

-- Add constraint to ensure pricing_model has valid structure when present
ALTER TABLE public.projects 
ADD CONSTRAINT pricing_model_structure_check 
CHECK (
  pricing_model IS NULL OR (
    pricing_model ? 'type' AND 
    pricing_model->>'type' IN ('subscription', 'commission', 'freemium', 'usage_based', 'one_time', 'hybrid')
  )
);

-- Create a view for easy pricing model analysis
CREATE OR REPLACE VIEW pricing_model_summary AS
SELECT 
  pricing_model->>'type' as pricing_type,
  COUNT(*) as project_count,
  CASE 
    WHEN pricing_model->>'type' = 'subscription' THEN 
      AVG(CAST(REGEXP_REPLACE(
        pricing_model->'subscription'->'plans'->0->>'price', 
        '[^0-9.]', '', 'g'
      ) AS NUMERIC))
    ELSE NULL
  END as avg_subscription_price
FROM public.projects 
WHERE pricing_model IS NOT NULL
GROUP BY pricing_model->>'type'
ORDER BY project_count DESC;

-- Grant necessary permissions
GRANT SELECT ON pricing_model_summary TO authenticated;
GRANT SELECT ON pricing_model_summary TO anon;

-- Add comment explaining the new structure
COMMENT ON COLUMN public.projects.pricing_model IS 
'JSONB column storing comprehensive pricing model information. Supports subscription, commission, freemium, usage_based, one_time, and hybrid pricing models. Structure varies by type but always includes a "type" field.';
