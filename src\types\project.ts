// Pricing model interfaces
export interface SubscriptionPlan {
  name: string;
  price: string;
  billing_period: 'monthly' | 'yearly' | 'one-time';
  features: string[];
  limits?: Record<string, number | string>;
  popular?: boolean;
}

export interface CommissionModel {
  commission_rate: number; // Percentage (e.g., 5 for 5%)
  minimum_payout?: number;
  payment_frequency: 'weekly' | 'monthly' | 'quarterly';
  description: string;
}

export interface FreemiumModel {
  free_tier: {
    name: string;
    features: string[];
    limits: Record<string, number | string>;
  };
  paid_tiers: SubscriptionPlan[];
}

export interface UsageBasedModel {
  base_price?: string; // Optional base subscription
  usage_tiers: {
    name: string;
    unit: string; // e.g., "API calls", "GB storage", "documents"
    price_per_unit: string;
    included_units?: number;
    overage_price?: string;
  }[];
  description: string;
}

export interface PricingModel {
  type: 'subscription' | 'commission' | 'freemium' | 'usage_based' | 'one_time' | 'hybrid';
  subscription?: {
    plans: SubscriptionPlan[];
    description?: string;
  };
  commission?: CommissionModel;
  freemium?: FreemiumModel;
  usage_based?: UsageBasedModel;
  one_time?: {
    price: string;
    features: string[];
    description: string;
  };
  hybrid?: {
    description: string;
    models: ('subscription' | 'commission' | 'usage_based')[];
  };
}

// Project content structure for JSON storage
export interface ProjectContent {
  description?: string;
  features?: string[];
  requirements?: string[];
  tech_stack?: string[];
  difficulty?: 'Easy' | 'Medium' | 'Hard';
  estimated_hours?: number;
  additional_notes?: string;
  // Legacy support - will be migrated to pricing_model
  subscription_plan?: {
    plan_name: string;
    price: string;
    features: string[];
  };
  // New comprehensive pricing model
  pricing_model?: PricingModel;
}

// Color scheme interface for AI-generated color codes
export interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  success: string;
  warning: string;
  error: string;
  neutral: string;
  description: string;
}

// Main project interface
export interface Project {
  id: string;
  name: string;
  description: string;
  category: string;
  target_user: string;
  status: string;
  claimed_by: string | null;
  content: ProjectContent | null;
  pricing_tier: number;
  landing_page_image: string | null; // URL to generated landing page image stored in Supabase Storage
  color_code: ColorScheme | null; // AI-generated color scheme for the project
  created_at: string;
  updated_at: string;
}

// Default project content structure
export const defaultProjectContent: ProjectContent = {
  description: '',
  features: [],
  requirements: [],
  tech_stack: [],
  difficulty: 'Medium',
  estimated_hours: 40,
  additional_notes: '',
  pricing_model: {
    type: 'subscription',
    subscription: {
      plans: [{
        name: 'Pro Plan',
        price: '$15/month',
        billing_period: 'monthly',
        features: ['Unlimited access to all features'],
        popular: true
      }],
      description: 'Simple monthly subscription model'
    }
  }
};

// Helper function to ensure content has proper structure
export const normalizeProjectContent = (content: any): ProjectContent => {
  if (!content || typeof content !== 'object') {
    return defaultProjectContent;
  }

  // Migrate legacy subscription_plan to new pricing_model format
  let pricingModel = content.pricing_model;
  if (!pricingModel && content.subscription_plan) {
    pricingModel = {
      type: 'subscription',
      subscription: {
        plans: [{
          name: content.subscription_plan.plan_name || 'Pro Plan',
          price: content.subscription_plan.price || '$15/month',
          billing_period: 'monthly' as const,
          features: Array.isArray(content.subscription_plan.features) ? content.subscription_plan.features : [],
          popular: true
        }],
        description: 'Monthly subscription plan'
      }
    };
  }

  return {
    description: content.description || '',
    features: Array.isArray(content.features) ? content.features : [],
    requirements: Array.isArray(content.requirements) ? content.requirements : [],
    tech_stack: Array.isArray(content.tech_stack) ? content.tech_stack : [],
    difficulty: ['Easy', 'Medium', 'Hard'].includes(content.difficulty) ? content.difficulty : 'Medium',
    estimated_hours: typeof content.estimated_hours === 'number' ? content.estimated_hours : 40,
    additional_notes: content.additional_notes || '',
    subscription_plan: content.subscription_plan, // Keep for backward compatibility
    pricing_model: pricingModel || defaultProjectContent.pricing_model
  };
};

// Helper function to get pricing display info
export const getPricingDisplayInfo = (content: ProjectContent) => {
  const pricingModel = content.pricing_model;
  if (!pricingModel) {
    // Fallback to legacy subscription_plan
    if (content.subscription_plan) {
      return {
        type: 'subscription',
        primaryPrice: content.subscription_plan.price,
        planCount: 1,
        description: content.subscription_plan.plan_name
      };
    }
    return null;
  }

  switch (pricingModel.type) {
    case 'subscription':
      return {
        type: 'subscription',
        primaryPrice: pricingModel.subscription?.plans[0]?.price || 'N/A',
        planCount: pricingModel.subscription?.plans.length || 0,
        description: `${pricingModel.subscription?.plans.length || 0} plan${(pricingModel.subscription?.plans.length || 0) > 1 ? 's' : ''}`
      };
    case 'commission':
      return {
        type: 'commission',
        primaryPrice: `${pricingModel.commission?.commission_rate || 0}% commission`,
        planCount: 1,
        description: 'Commission-based model'
      };
    case 'freemium':
      return {
        type: 'freemium',
        primaryPrice: 'Free + Paid tiers',
        planCount: (pricingModel.freemium?.paid_tiers.length || 0) + 1,
        description: 'Freemium model'
      };
    case 'usage_based':
      return {
        type: 'usage_based',
        primaryPrice: 'Pay per use',
        planCount: pricingModel.usage_based?.usage_tiers.length || 0,
        description: 'Usage-based pricing'
      };
    case 'one_time':
      return {
        type: 'one_time',
        primaryPrice: pricingModel.one_time?.price || 'N/A',
        planCount: 1,
        description: 'One-time payment'
      };
    case 'hybrid':
      return {
        type: 'hybrid',
        primaryPrice: 'Multiple models',
        planCount: pricingModel.hybrid?.models.length || 0,
        description: 'Hybrid pricing model'
      };
    default:
      return null;
  }
};
