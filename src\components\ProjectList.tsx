import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Project, getPricingDisplayInfo } from "@/types/project";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  SidebarContent,
  SidebarHeader,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarTrigger,
  useSidebar
} from "@/components/ui/sidebar";
import { Filter, CheckCircle, Clock, Play, XCircle, ChevronRight, MousePointer2, BookOpen, User, Target, MapPin, FileText } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

// Filter Sidebar Component
const FilterSidebar = ({
  statusFilter,
  setStatusFilter,
  projects
}: {
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  projects: Project[];
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Unclaimed":
        return <XCircle className="h-4 w-4 text-gray-500" />;
      case "Claimed":
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case "In Progress":
        return <Play className="h-4 w-4 text-orange-500" />;
      case "Completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Filter className="h-4 w-4" />;
    }
  };

  const getStatusCount = (status: string) => {
    return projects.filter(project => project.status === status).length;
  };

  const getStatusBudget = (status: string) => {
    return projects
      .filter(project => project.status === status)
      .reduce((sum, project) => sum + (project.pricing_tier || 100), 0);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const statusOptions = ["Unclaimed", "Claimed", "In Progress", "Completed"];

  return (
    <div className="flex flex-col h-full">
      <SidebarHeader className="border-b border-sidebar-border bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 flex-shrink-0">
        <div className="flex items-center gap-3 px-4 py-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Filter className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-sidebar-foreground">Filters</h2>
            <p className="text-xs text-sidebar-foreground/60">Filter projects by status</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-2 flex-1 flex flex-col">
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-sidebar-foreground/70 mb-3 px-2">
            Filter by Status
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {statusOptions.map((status) => (
                <SidebarMenuItem key={status}>
                  <SidebarMenuButton
                    isActive={statusFilter === status}
                    onClick={() => setStatusFilter(status)}
                    className={`w-full justify-between transition-all duration-200 hover:scale-[1.02] ${
                      statusFilter === status
                        ? 'bg-blue-100 dark:bg-blue-900 border border-blue-200 dark:border-blue-800 shadow-sm'
                        : 'hover:bg-sidebar-accent/50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      {getStatusIcon(status)}
                      <span className="font-medium">{status}</span>
                    </div>
                    <Badge
                      variant={statusFilter === status ? "default" : "secondary"}
                      className={`ml-auto text-xs ${
                        statusFilter === status
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-100 text-gray-600'
                      }`}
                    >
                      {getStatusCount(status)}
                    </Badge>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="my-6" />

        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-sidebar-foreground/70 mb-3 px-2">
            Project Statistics
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <Card className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 border-gray-200 dark:border-gray-700 shadow-sm">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-sidebar-foreground/70 flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Total Projects:
                    </span>
                    <span className="font-bold text-sidebar-foreground text-lg">{projects.length}</span>
                  </div>
                  <Separator className="opacity-50" />
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-sidebar-foreground/70 flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Available Budget:
                    </span>
                    <span className="font-semibold text-green-600 dark:text-green-400">{formatCurrency(getStatusBudget("Unclaimed"))}</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-sidebar-foreground/70 flex items-center gap-2">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      In Progress:
                    </span>
                    <span className="font-semibold text-orange-600 dark:text-orange-400">{getStatusCount("In Progress")}</span>
                  </div>
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-sidebar-foreground/70 flex items-center gap-2">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                      Completed:
                    </span>
                    <span className="font-semibold text-emerald-600 dark:text-emerald-400">{getStatusCount("Completed")}</span>
                  </div>
                  <Separator className="opacity-50" />
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-sidebar-foreground/70 flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Total Paid:
                    </span>
                    <span className="font-semibold text-blue-600 dark:text-blue-400">{formatCurrency(getStatusBudget("Completed"))}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Spacer to push Private Tips to bottom */}
        <div className="flex-1"></div>

        {/* Private Tips Section - Always at bottom */}
        <div className="mt-auto pt-4 border-t border-sidebar-border">
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenuButton
                className="w-full justify-start transition-all duration-200 hover:scale-[1.02] hover:bg-sidebar-accent/50"
                onClick={() => window.open('/tips-login', '_blank')}
              >
                <div className="flex items-center gap-3">
                  <BookOpen className="h-4 w-4 text-purple-600" />
                  <span className="font-medium">Private Tips</span>
                </div>
              </SidebarMenuButton>
            </SidebarGroupContent>
          </SidebarGroup>
        </div>
      </SidebarContent>
    </div>
  );
};

interface ProjectListProps {
  renderSidebar?: boolean;
  statusFilter?: string;
  setStatusFilter?: (status: string) => void;
}

export function ProjectList({
  renderSidebar = false,
  statusFilter = "Unclaimed",
  setStatusFilter = () => {}
}: ProjectListProps) {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("100");

  useEffect(() => {
    fetchProjects();
  }, []);

  // Helper function to check if project has AI-generated content
  const hasAIGeneratedContent = (project: Project): boolean => {
    if (!project.content || typeof project.content !== 'object') {
      return false;
    }

    // Check for AI-generated content structure (MVPResponse fields)
    const content = project.content as any;
    return !!(
      content.project_description ||
      content.main_user_features ||
      content.homepage ||
      content.user_journey ||
      content.admin_features ||
      content.flow_diagram
    );
  };

  const fetchProjects = async () => {
    try {
      const { data, error } = await supabase
        .from("projects")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Map data to Project type with default values for missing fields
      const projectsWithDefaults = (data || []).map(project => ({
        ...project,
        pricing_tier: (project as any).pricing_tier || 100,
        landing_page_image: (project as any).landing_page_image || null,
        color_code: (project as any).color_code || null,
        content: project.content as any
      })) as Project[];

      // Sort projects: AI-generated content first, then projects without content
      const allProjects = projectsWithDefaults.sort((a, b) => {
        const aHasContent = hasAIGeneratedContent(a);
        const bHasContent = hasAIGeneratedContent(b);

        // If both have content or both don't have content, maintain original order
        if (aHasContent === bHasContent) return 0;

        // Projects with content come first
        return bHasContent ? 1 : -1;
      });

      setProjects(allProjects);
    } catch (error) {
      console.error("Error fetching projects:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleProjectClick = (project: Project) => {
    navigate(`/project/${project.id}`);
  };

  // Helper function to get projects by pricing tier and status
  const getProjectsByTierAndStatus = (tier: number): Project[] => {
    return projects.filter(project =>
      (project.pricing_tier || 100) === tier &&
      project.status === statusFilter
    );
  };

  // Helper function to get project count by tier and status
  const getProjectCountByTier = (tier: number): number => {
    return getProjectsByTierAndStatus(tier).length;
  };

  // Component to render project table for a specific tier
  const ProjectTable = ({ tierProjects, tier }: { tierProjects: Project[], tier: number }) => {
    if (tierProjects.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-gray-500">No ${tier} projects available yet.</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        <div className="flex items-center gap-2 text-sm text-gray-600 bg-blue-50/50 px-3 py-2 rounded-md border border-blue-100">
          <MousePointer2 className="h-4 w-4 text-blue-500" />
          <span>Click on any row to view project details</span>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
          <thead>
            <tr className="border-b border-border">
              <th className="text-left p-4 font-semibold">SaaS Idea Name</th>
              <th className="text-left p-4 font-semibold">Description</th>
              <th className="text-left p-4 font-semibold">Pricing Model</th>
              <th className="text-left p-4 font-semibold">Status</th>
              <th className="w-12"></th>
            </tr>
          </thead>
          <tbody>
            {tierProjects.map((project, index) => {
              const hasContent = hasAIGeneratedContent(project);
              const prevProject = index > 0 ? tierProjects[index - 1] : null;
              const prevHasContent = prevProject ? hasAIGeneratedContent(prevProject) : true;
              const showInReviewMessage = !hasContent && prevHasContent;

              return (
                <React.Fragment key={project.id}>
                  {showInReviewMessage && (
                    <tr>
                      <td colSpan={5} className="p-3 bg-red-100/50 border-t-2 border-red-200">
                        <div className="text-center text-red-700 text-sm font-medium">
                          📋 The following projects are currently in review and will be available for development soon
                        </div>
                      </td>
                    </tr>
                  )}
                  <tr
                  className={`group border-b border-border transition-all duration-200 ${
                    hasContent
                      ? 'hover:bg-blue-50/80 hover:shadow-sm cursor-pointer hover:border-blue-200/50'
                      : 'bg-red-50/50 border-red-200/50 cursor-not-allowed opacity-75'
                  }`}
                  onClick={hasContent ? () => handleProjectClick(project) : undefined}
                  role={hasContent ? "button" : undefined}
                  tabIndex={hasContent ? 0 : -1}
                  aria-label={hasContent ? `View details for ${project.name}` : `${project.name} - In review`}
                  onKeyDown={hasContent ? (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleProjectClick(project);
                    }
                  } : undefined}
                >
                  <td className={`p-4 font-medium transition-colors ${
                    hasContent
                      ? 'group-hover:text-blue-700'
                      : 'text-red-700'
                  }`}>
                    <div className="flex items-center gap-2">
                      {hasContent ? (
                        <MousePointer2 className="h-4 w-4 text-gray-400 group-hover:text-blue-500 transition-colors" />
                      ) : (
                        <div className="h-4 w-4 bg-red-400 rounded-full animate-pulse" />
                      )}
                      {project.name}
                    </div>
                  </td>
                  <td className={`p-4 transition-colors ${
                    hasContent
                      ? 'group-hover:text-gray-700'
                      : 'text-red-600'
                  }`}>
                    {project.description}
                  </td>
                  <td className="p-4">
                    {hasContent && project.content ? (() => {
                      const pricingInfo = getPricingDisplayInfo(project.content);
                      if (pricingInfo) {
                        return (
                          <div className="space-y-1">
                            <div className="text-sm font-medium text-gray-900">
                              {pricingInfo.primaryPrice}
                            </div>
                            <div className="text-xs text-gray-500 capitalize">
                              {pricingInfo.type.replace('_', ' ')} • {pricingInfo.planCount} plan{pricingInfo.planCount > 1 ? 's' : ''}
                            </div>
                          </div>
                        );
                      }
                      return <span className="text-gray-400 text-sm">No pricing info</span>;
                    })() : <span className="text-gray-400 text-sm">-</span>}
                  </td>
                  <td className="p-4">
                    <Badge variant={hasContent ? (project.status === 'Completed' ? 'default' : 'secondary') : 'destructive'}>
                      {hasContent ? project.status : 'In Review'}
                    </Badge>
                  </td>
                  <td className="p-4 text-right">
                    {hasContent ? (
                      <ChevronRight className="h-4 w-4 text-gray-400 group-hover:text-blue-500 group-hover:translate-x-1 transition-all duration-200" />
                    ) : (
                      <div className="h-4 w-4 text-red-400">⏳</div>
                    )}
                  </td>
                </tr>
                </React.Fragment>
              );
            })}
          </tbody>
          </table>
        </div>
      </div>
    );
  };

  if (loading) {
    return <div className="p-8 text-center">Loading projects...</div>;
  }

  if (projects.length === 0) {
    return (
      <div className="p-8 text-center">
        <h2 className="text-xl font-semibold text-gray-700 mb-2">No Projects Yet</h2>
        <p className="text-gray-500">
          Projects will appear here once they have been generated using AI.
          Visit the admin panel to generate project specifications.
        </p>
      </div>
    );
  }

  // If rendering sidebar, only return the sidebar content
  if (renderSidebar) {
    return (
      <FilterSidebar
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        projects={projects}
      />
    );
  }

  // Otherwise render the main content
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          {/* About This Project Paragraph */}
          <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-gray-700 leading-relaxed">
              This project was created in just 2 hours using vibe coding. Each project is generated using AI.
              We integrated Flux for image generation, Gemini API, Cloudflare Image Generation, and more.
              If you prefer traditional development methods, this approach might not be for you.
              These projects are simple and can be completed in just a few hours if you master the right tools and techniques.{' '}
              <Button
                variant="link"
                className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium"
                onClick={() => window.open('/private-tips', '_blank')}
              >
                You can see the tips here
              </Button>
              .
            </p>

            {/* About Me Button */}
            <div className="mt-4 text-center space-y-3">
              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    className="bg-white hover:bg-gray-50 border-blue-300 text-blue-700 hover:text-blue-800 font-medium"
                  >
                    <User className="h-4 w-4 mr-2" />
                    About me and the goal why I am doing this
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
                      <User className="h-5 w-5 text-blue-600" />
                      About Me & My Goal
                    </DialogTitle>
                  </DialogHeader>
                  <div className="space-y-6 mt-4">
                    {/* About Me Section */}
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                      <h3 className="text-lg font-semibold text-blue-800 mb-3 flex items-center gap-2">
                        <User className="h-5 w-5" />
                        About me
                      </h3>
                      <div className="space-y-3 text-gray-700">
                        <p>I own 10 SaaS platforms that I built myself in 10 days.</p>
                        <Button
                          variant="link"
                          className="p-0 h-auto text-blue-600 hover:text-blue-800 font-medium"
                          onClick={() => window.open('/private-tips', '_blank')}
                        >
                          See the tips I used to build 1 SaaS per day
                        </Button>
                      </div>
                    </div>

                    {/* Goal Section */}
                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                      <h3 className="text-lg font-semibold text-green-800 mb-3 flex items-center gap-2">
                        <Target className="h-5 w-5" />
                        Goal
                      </h3>
                      <div className="space-y-3 text-gray-700 leading-relaxed">
                        <p>
                          My goal is to build a portfolio of 100+ SaaS projects for myself. I don't expect each app to be bug-free.
                          Each project should be completed within a $100 budget. If you can build more than one, feel free to take on
                          multiple projects. (we can create multiple contracts for each)
                        </p>
                        <p>
                          The reason for the $50 - $200 budget per project is that I don't expect perfect SaaS apps without bugs or
                          advanced features.
                        </p>
                        <p className="font-medium text-green-800">
                          I only want the basic features that every SaaS should have.
                        </p>
                      </div>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>

              {/* How to Proceed Button */}
              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    className="bg-white hover:bg-gray-50 border-green-300 text-green-700 hover:text-green-800 font-medium"
                  >
                    <MapPin className="h-4 w-4 mr-2" />
                    How to Proceed
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle className="text-xl font-bold text-gray-800 flex items-center gap-2">
                      <MapPin className="h-5 w-5 text-green-600" />
                      How to Proceed
                    </DialogTitle>
                  </DialogHeader>
                  <div className="mt-4">
                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                      <ol className="space-y-3 text-gray-700 leading-relaxed">
                        <li className="flex items-start gap-3">
                          <span className="w-6 h-6 rounded-full bg-green-600 text-white text-xs flex items-center justify-center font-bold mt-0.5 flex-shrink-0">
                            1
                          </span>
                          <span>Select a project from this list below</span>
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-6 h-6 rounded-full bg-green-600 text-white text-xs flex items-center justify-center font-bold mt-0.5 flex-shrink-0">
                            2
                          </span>
                          <span>Read and understand what I expect by reading the project details</span>
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-6 h-6 rounded-full bg-green-600 text-white text-xs flex items-center justify-center font-bold mt-0.5 flex-shrink-0">
                            3
                          </span>
                          <span>Let me know which project you've selected & you will then have access to the tips section</span>
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-6 h-6 rounded-full bg-green-600 text-white text-xs flex items-center justify-center font-bold mt-0.5 flex-shrink-0">
                            4
                          </span>
                          <span>Once we agree, I will send you the offer</span>
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-6 h-6 rounded-full bg-green-600 text-white text-xs flex items-center justify-center font-bold mt-0.5 flex-shrink-0">
                            5
                          </span>
                          <div className="flex-1">
                            <span>You start working on the project you selected by following steps defined in the project</span>
                          </div>
                        </li>
                        <li className="flex items-start gap-3">
                          <span className="w-6 h-6 rounded-full bg-green-600 text-white text-xs flex items-center justify-center font-bold mt-0.5 flex-shrink-0">
                            6
                          </span>
                          <span>After you complete the project as per the definition of done, I will release the payment</span>
                        </li>
                      </ol>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          <p className="text-sm text-gray-600">
            Total: {projects.length} Project{projects.length !== 1 ? 's' : ''}
            {statusFilter !== "Unclaimed" && (
              <span className="ml-2 text-blue-600">
                (Filtered by: {statusFilter})
              </span>
            )}
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="50" className="flex items-center gap-2">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                $50
              </Badge>
              Simple ({getProjectCountByTier(50)})
            </TabsTrigger>
            <TabsTrigger value="100" className="flex items-center gap-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                $100
              </Badge>
              Standard ({getProjectCountByTier(100)})
            </TabsTrigger>
            <TabsTrigger value="200" className="flex items-center gap-2">
              <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                $200
              </Badge>
              Complex ({getProjectCountByTier(200)})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="50" className="mt-6">
            <ProjectTable tierProjects={getProjectsByTierAndStatus(50)} tier={50} />
          </TabsContent>

          <TabsContent value="100" className="mt-6">
            <ProjectTable tierProjects={getProjectsByTierAndStatus(100)} tier={100} />
          </TabsContent>

          <TabsContent value="200" className="mt-6">
            <ProjectTable tierProjects={getProjectsByTierAndStatus(200)} tier={200} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}