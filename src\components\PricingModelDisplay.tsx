import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DollarSign, Crown, Zap, TrendingUp, Gift, Layers } from 'lucide-react';
import { PricingModel, SubscriptionPlan } from '@/types/project';

interface PricingModelDisplayProps {
  pricingModel: PricingModel;
  legacySubscriptionPlan?: {
    plan_name: string;
    price: string;
    features: string[];
  };
}

const PricingModelDisplay: React.FC<PricingModelDisplayProps> = ({ 
  pricingModel, 
  legacySubscriptionPlan 
}) => {
  // Handle legacy subscription plan format
  if (!pricingModel && legacySubscriptionPlan) {
    return (
      <Card className="border-green-200 bg-green-50/50">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold text-green-800 flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            {legacySubscriptionPlan.plan_name || 'Subscription Plan'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-green-600">{legacySubscriptionPlan.price}</span>
            </div>
            {legacySubscriptionPlan.features && Array.isArray(legacySubscriptionPlan.features) && (
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">Features:</h4>
                <ul className="space-y-1">
                  {legacySubscriptionPlan.features.map((feature: string, index: number) => (
                    <li key={index} className="flex items-start gap-2 text-gray-700">
                      <span className="text-green-600 mt-1">•</span>
                      <span className="text-sm leading-relaxed">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>📝 Implementation Note:</strong> This is the only subscription plan you need to implement for this project.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!pricingModel) return null;

  const getIcon = (type: string) => {
    switch (type) {
      case 'subscription': return <Crown className="h-5 w-5" />;
      case 'commission': return <TrendingUp className="h-5 w-5" />;
      case 'freemium': return <Gift className="h-5 w-5" />;
      case 'usage_based': return <Zap className="h-5 w-5" />;
      case 'one_time': return <DollarSign className="h-5 w-5" />;
      case 'hybrid': return <Layers className="h-5 w-5" />;
      default: return <DollarSign className="h-5 w-5" />;
    }
  };

  const getColor = (type: string) => {
    switch (type) {
      case 'subscription': return 'border-blue-200 bg-blue-50/50 text-blue-800';
      case 'commission': return 'border-purple-200 bg-purple-50/50 text-purple-800';
      case 'freemium': return 'border-green-200 bg-green-50/50 text-green-800';
      case 'usage_based': return 'border-orange-200 bg-orange-50/50 text-orange-800';
      case 'one_time': return 'border-indigo-200 bg-indigo-50/50 text-indigo-800';
      case 'hybrid': return 'border-gray-200 bg-gray-50/50 text-gray-800';
      default: return 'border-gray-200 bg-gray-50/50 text-gray-800';
    }
  };

  const renderSubscriptionPlans = (plans: SubscriptionPlan[]) => (
    <div className="space-y-4">
      {plans.map((plan, index) => (
        <div key={index} className="border rounded-lg p-4 bg-white/50">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-semibold text-gray-900">{plan.name}</h4>
            <div className="flex items-center gap-2">
              <span className="text-xl font-bold text-blue-600">{plan.price}</span>
              {plan.popular && <Badge variant="secondary">Popular</Badge>}
            </div>
          </div>
          <p className="text-sm text-gray-600 mb-3">Billed {plan.billing_period}</p>
          <ul className="space-y-1">
            {plan.features.map((feature, fIndex) => (
              <li key={fIndex} className="flex items-start gap-2 text-gray-700">
                <span className="text-blue-600 mt-1">•</span>
                <span className="text-sm leading-relaxed">{feature}</span>
              </li>
            ))}
          </ul>
          {plan.limits && Object.keys(plan.limits).length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <h5 className="text-sm font-medium text-gray-900 mb-1">Limits:</h5>
              <div className="text-sm text-gray-600">
                {Object.entries(plan.limits).map(([key, value]) => (
                  <span key={key} className="mr-3">{key}: {value}</span>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );

  const renderPricingContent = () => {
    switch (pricingModel.type) {
      case 'subscription':
        return pricingModel.subscription ? (
          <div className="space-y-4">
            {pricingModel.subscription.description && (
              <p className="text-gray-700">{pricingModel.subscription.description}</p>
            )}
            {renderSubscriptionPlans(pricingModel.subscription.plans)}
          </div>
        ) : null;

      case 'commission':
        return pricingModel.commission ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-purple-600">
                {pricingModel.commission.commission_rate}%
              </span>
              <span className="text-gray-600">commission per transaction</span>
            </div>
            <p className="text-gray-700">{pricingModel.commission.description}</p>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Payment Frequency:</span>
                <p className="text-gray-600 capitalize">{pricingModel.commission.payment_frequency}</p>
              </div>
              {pricingModel.commission.minimum_payout && (
                <div>
                  <span className="font-medium">Minimum Payout:</span>
                  <p className="text-gray-600">${pricingModel.commission.minimum_payout}</p>
                </div>
              )}
            </div>
          </div>
        ) : null;

      case 'freemium':
        return pricingModel.freemium ? (
          <div className="space-y-4">
            {/* Free Tier */}
            <div className="border rounded-lg p-4 bg-green-50/30">
              <h4 className="font-semibold text-gray-900 mb-2">{pricingModel.freemium.free_tier.name}</h4>
              <p className="text-lg font-bold text-green-600 mb-3">Free</p>
              <ul className="space-y-1 mb-3">
                {pricingModel.freemium.free_tier.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2 text-gray-700">
                    <span className="text-green-600 mt-1">•</span>
                    <span className="text-sm leading-relaxed">{feature}</span>
                  </li>
                ))}
              </ul>
              {pricingModel.freemium.free_tier.limits && (
                <div className="pt-2 border-t border-green-200">
                  <h5 className="text-sm font-medium text-gray-900 mb-1">Free Tier Limits:</h5>
                  <div className="text-sm text-gray-600">
                    {Object.entries(pricingModel.freemium.free_tier.limits).map(([key, value]) => (
                      <span key={key} className="mr-3">{key}: {value}</span>
                    ))}
                  </div>
                </div>
              )}
            </div>
            {/* Paid Tiers */}
            {renderSubscriptionPlans(pricingModel.freemium.paid_tiers)}
          </div>
        ) : null;

      case 'usage_based':
        return pricingModel.usage_based ? (
          <div className="space-y-4">
            {pricingModel.usage_based.base_price && (
              <div className="flex items-center gap-2">
                <span className="text-xl font-bold text-orange-600">{pricingModel.usage_based.base_price}</span>
                <span className="text-gray-600">base price</span>
              </div>
            )}
            <p className="text-gray-700">{pricingModel.usage_based.description}</p>
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900">Usage Tiers:</h4>
              {pricingModel.usage_based.usage_tiers.map((tier, index) => (
                <div key={index} className="border rounded-lg p-3 bg-white/50">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium">{tier.name}</span>
                    <span className="font-bold text-orange-600">{tier.price_per_unit} per {tier.unit}</span>
                  </div>
                  {tier.included_units && (
                    <p className="text-sm text-gray-600">Includes {tier.included_units} {tier.unit}</p>
                  )}
                  {tier.overage_price && (
                    <p className="text-sm text-gray-600">Overage: {tier.overage_price} per {tier.unit}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        ) : null;

      case 'one_time':
        return pricingModel.one_time ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold text-indigo-600">{pricingModel.one_time.price}</span>
              <span className="text-gray-600">one-time payment</span>
            </div>
            <p className="text-gray-700">{pricingModel.one_time.description}</p>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">What's Included:</h4>
              <ul className="space-y-1">
                {pricingModel.one_time.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2 text-gray-700">
                    <span className="text-indigo-600 mt-1">•</span>
                    <span className="text-sm leading-relaxed">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ) : null;

      case 'hybrid':
        return pricingModel.hybrid ? (
          <div className="space-y-3">
            <p className="text-gray-700">{pricingModel.hybrid.description}</p>
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Pricing Models Used:</h4>
              <div className="flex flex-wrap gap-2">
                {pricingModel.hybrid.models.map((model, index) => (
                  <Badge key={index} variant="outline" className="capitalize">
                    {model.replace('_', ' ')}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        ) : null;

      default:
        return <p className="text-gray-500">Unknown pricing model type</p>;
    }
  };

  return (
    <Card className={getColor(pricingModel.type)}>
      <CardHeader className="pb-3">
        <CardTitle className={`text-lg font-semibold flex items-center gap-2 ${getColor(pricingModel.type).split(' ')[2]}`}>
          {getIcon(pricingModel.type)}
          {pricingModel.type.charAt(0).toUpperCase() + pricingModel.type.slice(1).replace('_', ' ')} Pricing
        </CardTitle>
      </CardHeader>
      <CardContent>
        {renderPricingContent()}
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>📝 Implementation Note:</strong> Implement this {pricingModel.type} pricing model for the project.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default PricingModelDisplay;
