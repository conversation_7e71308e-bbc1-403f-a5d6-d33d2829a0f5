Title: Calling "Vibe Coders": Pick & Build a SaaS App from Our Backlog ($100 fixed-priced each project)


I'm looking for several skilled vibe coders to help build a portfolio of 100+ micro-SaaS apps.

Each completed app has a fixed budget of $100. ($100 x 100 = $10,000 potential)

The reason for the $100 budget per project is that, I don’t expect perfect, bug-free apps, just the essential core features. (See an example of what I expect here: https://drive.google.com/file/d/11uaQAP20ruM6yeXGFiIovI8OdM6gOVhI)

Instead of assigning you a job, I do things differently. I maintain a private, curated list of over 113+ micro-SaaS ideas. My goal is to find talented developers who can browse this list, claim a project they find interesting, and build it.

Here’s the simple process:
---------------------------
- You apply by following the instructions in the "To Apply" section below.
- I will review your application, including your skills and "vibe check" link.
- If we're a good fit, you will receive a private link to our project portal.
- You choose a project. We then have a brief planning chat where you'll propose a specific Dribbble/Behance design for it and outline your development plan (including your understanding of the project and the tools you'll use, like Cursor, RooCode, etc.).
- After we agree on the plan, I will send you an offer for the full $100, structured into the 3 milestones detailed below.

Milestone Payment:
------------------
(1) $25 - Milestone 1: UI Scaffolding & Visual Approval
Based on the project-specific visual direction we agree upon, your first task is to use AI tools to generate the static UI for all required pages (Homepage, Dashboard, Login, etc.). This step is purely for visual alignment.
(Deliverable: A complete set of screenshots for all static pages for final approval + also a .zip file of the code)

(2) $50 - Milestone 2: Core Logic & User Functionality
Once the full UI is approved, you will integrate Supabase, build the database tables, and implement the complete user authentication and core features of the project. This milestone is 100% focused on making the application work for the end-user.
(Deliverable: You can send a short screen recording (using Loom) to demonstrate the full user flow and its features or send me a .zip for me to test)

(3) $25 - Milestone 3: Admin Flow & Finalization
In the final stage, you will integrate Stripe payments (in test mode), build the admin panel, and ensure every single item in the "Definition of Done" is met and fully functional.
(Deliverable: The complete, final codebase in a .zip file.)

Ideal Candidate Profile:
-------------------------
- You are an expert with React and Supabase. This is a strict requirement.
- You are an "AI-First" developer. You don't just write code; you use modern AI assistants and rapid-development tools to build things incredibly fast.
- You know how to use advanced AI tools like MCP Supabase and other similar ones.
- You’re okay with guiding AI to do the work instead of writing all the code yourself.
- Expert in debugging when AI needs help & make sure to learn about context engineering and prompt engineering to help prevent the AI from hallucinating.

Follow these guidelines, failure to do so may result in refactoring requests:
-----------------------------------------------------------------------------
- React + TypeScript + Tailwind CSS + Shadcn UI
- Supabase (Database, auth, policies, edge functions, storage if needed)
- Use Supabase Edge Functions for backend logic. Do not use the /api folder or any server-side rendering (SSR). This is meant to be a client-side rendered (CSR) app, so Next.js should not be used.
- Stripe for payments
- Keep components small and modular. Avoid files that are over 1,000 lines. Break down larger logic into reusable subcomponents wherever possible.

To Apply:
----------
To be considered, please provide the following 3 items in your application:

- Start your application with the word "CHOICE".
- Briefly tell me about a project you built very quickly and what tools or methods you used to achieve that speed.
- To understand the design quality and modern aesthetic I expect, review my portfolio of personally-built projects here: https://photos.app.goo.gl/dgXionR4uMjA9YPC7